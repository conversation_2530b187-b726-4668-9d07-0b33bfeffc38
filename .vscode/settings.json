{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  // format flow
  "editor.codeActionsOnSave": ["source.formatDocument", "source.organizeImports.sortImports"],
  "eslint.validate": ["typescript"],
  // https://marketplace.visualstudio.com/items?itemName=dozerg.tsimportsorter
  "tsImportSorter.configuration.groupRules": [
    ["^@\\w", "^\\w"],
    ["^@/"],
    ["^\\.\\."],
    ["^\\."],
    { "flags": "scripts" }
  ],
  "tsImportSorter.configuration.keepUnused": [".*"],
  "tsImportSorter.configuration.wrappingStyle": {
    "maxNamesPerWrappedLine": 0,
    "maxBindingNamesPerLine": 0,
    "maxDefaultAndBindingNamesPerLine": 0
  },
  // aaron-bond.better-comments
  "better-comments.multilineComments": true,
  "better-comments.tags": [
    {
      "tag": "!",
      "color": "#FF2D00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "?",
      "color": "#3498DB",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "//",
      "color": "#474747",
      "strikethrough": true,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "todo",
      "color": "#FF8C00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "*",
      "color": "#98C379",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    }
  ]
}
