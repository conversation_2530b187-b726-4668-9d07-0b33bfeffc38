{"extends": "@tsconfig/node14/tsconfig.json", "compilerOptions": {"target": "es6", "lib": ["es2019", "DOM"], "outDir": "./lib", "allowJs": true, "checkJs": true, "declaration": true, "declarationMap": true, "noImplicitAny": false, "esModuleInterop": true, "sourceMap": true, "jsx": "react", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["./src/**/*"], "exclude": ["node_modules"], "ts-node": {"files": true}}