{"name": "@raydium-io/raydium-sdk-v2", "version": "0.2.12-alpha", "description": "An SDK for building applications on top of Raydium.", "license": "GPL-3.0", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "files": ["./lib", "./src"], "repository": "https://github.com/raydium-io/raydium-sdk-V2", "publicConfig": {"registry": "https://registry.yarnpkg.com", "access": "public"}, "keywords": ["raydium", "solana"], "lint-staged": {"{src,test,misc}/**/*.ts": ["yarn lint"]}, "scripts": {"lint": "eslint src/**/*.ts", "test": "jest", "build-docs": "typedoc", "build-docs-watch": "typedoc --watch", "build-dist": "rm -rf dist && mkdir dist && yarn build-docs", "build-lib": "rm -rf lib && tsup src --format cjs,esm --dts --sourcemap --no-splitting --minify -d lib --silent", "build-all": "yarn build-dist && yarn build-lib", "build": "yarn build-lib", "push": "yarn build && yarn publish", "prepare": "husky install", "dev": "ts-node -r tsconfig-paths/register", "watch": "rm -rf lib && tsup src --watch --format cjs,esm --dts --sourcemap --no-splitting --minify -d lib"}, "dependencies": {"@solana/buffer-layout": "^4.0.1", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "^1.95.3", "axios": "^1.1.3", "big.js": "^6.2.1", "bn.js": "^5.2.1", "dayjs": "^1.11.5", "decimal.js-light": "^2.5.1", "jsonfile": "^6.1.0", "lodash": "^4.17.21", "toformat": "^2.0.0", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@babel/core": "^7.18.0", "@babel/preset-env": "^7.18.0", "@babel/preset-typescript": "^7.17.12", "@tsconfig/node14": "^1.0.3", "@types/big.js": "^6.1.3", "@types/bn.js": "^5.1.1", "@types/jest": "^27.5.1", "@types/lodash": "^4.14.182", "@types/node": "^17.0.35", "@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "babel-jest": "^27.5.1", "eslint": "^8.15.0", "eslint-plugin-tsdoc": "^0.2.16", "husky": "^7.0.4", "jest": "^27.5.1", "lint-staged": "^12.4.1", "prettier": "^2.6.2", "ts-jest": "^27.0.7", "ts-jest-resolver": "^2.0.0", "ts-node": "^10.7.0", "tsup": "^5.12.8", "typedoc": "^0.22.15", "typescript": "^4.7.3", "yarn": "^1.22.19"}}